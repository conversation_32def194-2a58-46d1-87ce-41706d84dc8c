{"version": 3, "file": "carousel.js", "sources": ["../src/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "KEY_TO_DIRECTION", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "pause", "ride", "touch", "wrap", "DefaultType", "Carousel", "BaseComponent", "constructor", "element", "config", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "SelectorEngine", "findOne", "_element", "_addEventListeners", "_config", "cycle", "next", "_slide", "nextWhenVisible", "document", "hidden", "isVisible", "prev", "triggerTransitionEnd", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "EventHandler", "one", "to", "index", "items", "_getItems", "length", "activeIndex", "_getItemIndex", "_getActive", "order", "dispose", "_configAfterMerge", "defaultInterval", "on", "event", "_keydown", "Swipe", "isSupported", "_addTouchEventListeners", "img", "find", "preventDefault", "endCallBack", "clearTimeout", "setTimeout", "swipeConfig", "leftCallback", "_directionToOrder", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "test", "target", "tagName", "direction", "key", "indexOf", "_setActiveIndicatorElement", "activeIndicator", "classList", "remove", "removeAttribute", "newActiveIndicator", "add", "setAttribute", "elementInterval", "Number", "parseInt", "getAttribute", "activeElement", "isNext", "nextElement", "getNextActiveElement", "nextElementIndex", "triggerEvent", "eventName", "trigger", "relatedTarget", "_orderToDirection", "from", "slideEvent", "defaultPrevented", "isCycling", "Boolean", "directionalClassName", "orderClassName", "reflow", "completeCallBack", "_queueCallback", "_isAnimated", "contains", "clearInterval", "isRTL", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "getElementFromSelector", "carousel", "slideIndex", "Manipulator", "getDataAttribute", "window", "carousels", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAgBA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAU;EACvB,MAAMC,QAAQ,GAAG,aAAa;EAC9B,MAAMC,SAAS,GAAG,CAAA,CAAA,EAAID,QAAQ,CAAA,CAAE;EAChC,MAAME,YAAY,GAAG,WAAW;EAEhC,MAAMC,cAAc,GAAG,WAAW;EAClC,MAAMC,eAAe,GAAG,YAAY;EACpC,MAAMC,sBAAsB,GAAG,GAAG,CAAA;;EAElC,MAAMC,UAAU,GAAG,MAAM;EACzB,MAAMC,UAAU,GAAG,MAAM;EACzB,MAAMC,cAAc,GAAG,MAAM;EAC7B,MAAMC,eAAe,GAAG,OAAO;EAE/B,MAAMC,WAAW,GAAG,CAAA,KAAA,EAAQT,SAAS,CAAA,CAAE;EACvC,MAAMU,UAAU,GAAG,CAAA,IAAA,EAAOV,SAAS,CAAA,CAAE;EACrC,MAAMW,aAAa,GAAG,CAAA,OAAA,EAAUX,SAAS,CAAA,CAAE;EAC3C,MAAMY,gBAAgB,GAAG,CAAA,UAAA,EAAaZ,SAAS,CAAA,CAAE;EACjD,MAAMa,gBAAgB,GAAG,CAAA,UAAA,EAAab,SAAS,CAAA,CAAE;EACjD,MAAMc,gBAAgB,GAAG,CAAA,SAAA,EAAYd,SAAS,CAAA,CAAE;EAChD,MAAMe,mBAAmB,GAAG,CAAA,IAAA,EAAOf,SAAS,CAAA,EAAGC,YAAY,CAAA,CAAE;EAC7D,MAAMe,oBAAoB,GAAG,CAAA,KAAA,EAAQhB,SAAS,CAAA,EAAGC,YAAY,CAAA,CAAE;EAE/D,MAAMgB,mBAAmB,GAAG,UAAU;EACtC,MAAMC,iBAAiB,GAAG,QAAQ;EAClC,MAAMC,gBAAgB,GAAG,OAAO;EAChC,MAAMC,cAAc,GAAG,mBAAmB;EAC1C,MAAMC,gBAAgB,GAAG,qBAAqB;EAC9C,MAAMC,eAAe,GAAG,oBAAoB;EAC5C,MAAMC,eAAe,GAAG,oBAAoB;EAE5C,MAAMC,eAAe,GAAG,SAAS;EACjC,MAAMC,aAAa,GAAG,gBAAgB;EACtC,MAAMC,oBAAoB,GAAGF,eAAe,GAAGC,aAAa;EAC5D,MAAME,iBAAiB,GAAG,oBAAoB;EAC9C,MAAMC,mBAAmB,GAAG,sBAAsB;EAClD,MAAMC,mBAAmB,GAAG,qCAAqC;EACjE,MAAMC,kBAAkB,GAAG,2BAA2B;EAEtD,MAAMC,gBAAgB,GAAG;IACvB,CAAC7B,cAAc,GAAGM,eAAe;EACjC,EAAA,CAACL,eAAe,GAAGI;EACrB,CAAC;EAED,MAAMyB,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,KAAK,EAAE,OAAO;EACdC,EAAAA,IAAI,EAAE,KAAK;EACXC,EAAAA,KAAK,EAAE,IAAI;EACXC,EAAAA,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBAAkB;EAAE;EAC9BC,EAAAA,QAAQ,EAAE,SAAS;EACnBC,EAAAA,KAAK,EAAE,kBAAkB;EACzBC,EAAAA,IAAI,EAAE,kBAAkB;EACxBC,EAAAA,KAAK,EAAE,SAAS;EAChBC,EAAAA,IAAI,EAAE;EACR,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,QAAQ,SAASC,aAAa,CAAC;EACnCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC;MAEtB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,YAAY,GAAG,IAAI;EAExB,IAAA,IAAI,CAACC,kBAAkB,GAAGC,cAAc,CAACC,OAAO,CAACxB,mBAAmB,EAAE,IAAI,CAACyB,QAAQ,CAAC;MACpF,IAAI,CAACC,kBAAkB,EAAE;EAEzB,IAAA,IAAI,IAAI,CAACC,OAAO,CAACnB,IAAI,KAAKnB,mBAAmB,EAAE;QAC7C,IAAI,CAACuC,KAAK,EAAE;EACd,IAAA;EACF,EAAA;;EAEA;IACA,WAAWxB,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWO,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWzC,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;EACA2D,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAI,CAACC,MAAM,CAACrD,UAAU,CAAC;EACzB,EAAA;EAEAsD,EAAAA,eAAeA,GAAG;EAChB;EACA;EACA;MACA,IAAI,CAACC,QAAQ,CAACC,MAAM,IAAIC,kBAAS,CAAC,IAAI,CAACT,QAAQ,CAAC,EAAE;QAChD,IAAI,CAACI,IAAI,EAAE;EACb,IAAA;EACF,EAAA;EAEAM,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAI,CAACL,MAAM,CAACpD,UAAU,CAAC;EACzB,EAAA;EAEA6B,EAAAA,KAAKA,GAAG;MACN,IAAI,IAAI,CAACY,UAAU,EAAE;EACnBiB,MAAAA,6BAAoB,CAAC,IAAI,CAACX,QAAQ,CAAC;EACrC,IAAA;MAEA,IAAI,CAACY,cAAc,EAAE;EACvB,EAAA;EAEAT,EAAAA,KAAKA,GAAG;MACN,IAAI,CAACS,cAAc,EAAE;MACrB,IAAI,CAACC,eAAe,EAAE;EAEtB,IAAA,IAAI,CAACrB,SAAS,GAAGsB,WAAW,CAAC,MAAM,IAAI,CAACR,eAAe,EAAE,EAAE,IAAI,CAACJ,OAAO,CAACtB,QAAQ,CAAC;EACnF,EAAA;EAEAmC,EAAAA,iBAAiBA,GAAG;EAClB,IAAA,IAAI,CAAC,IAAI,CAACb,OAAO,CAACnB,IAAI,EAAE;EACtB,MAAA;EACF,IAAA;MAEA,IAAI,IAAI,CAACW,UAAU,EAAE;EACnBsB,MAAAA,YAAY,CAACC,GAAG,CAAC,IAAI,CAACjB,QAAQ,EAAE3C,UAAU,EAAE,MAAM,IAAI,CAAC8C,KAAK,EAAE,CAAC;EAC/D,MAAA;EACF,IAAA;MAEA,IAAI,CAACA,KAAK,EAAE;EACd,EAAA;IAEAe,EAAEA,CAACC,KAAK,EAAE;EACR,IAAA,MAAMC,KAAK,GAAG,IAAI,CAACC,SAAS,EAAE;MAC9B,IAAIF,KAAK,GAAGC,KAAK,CAACE,MAAM,GAAG,CAAC,IAAIH,KAAK,GAAG,CAAC,EAAE;EACzC,MAAA;EACF,IAAA;MAEA,IAAI,IAAI,CAACzB,UAAU,EAAE;EACnBsB,MAAAA,YAAY,CAACC,GAAG,CAAC,IAAI,CAACjB,QAAQ,EAAE3C,UAAU,EAAE,MAAM,IAAI,CAAC6D,EAAE,CAACC,KAAK,CAAC,CAAC;EACjE,MAAA;EACF,IAAA;MAEA,MAAMI,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC;MACzD,IAAIF,WAAW,KAAKJ,KAAK,EAAE;EACzB,MAAA;EACF,IAAA;MAEA,MAAMO,KAAK,GAAGP,KAAK,GAAGI,WAAW,GAAGvE,UAAU,GAAGC,UAAU;MAE3D,IAAI,CAACoD,MAAM,CAACqB,KAAK,EAAEN,KAAK,CAACD,KAAK,CAAC,CAAC;EAClC,EAAA;EAEAQ,EAAAA,OAAOA,GAAG;MACR,IAAI,IAAI,CAAC/B,YAAY,EAAE;EACrB,MAAA,IAAI,CAACA,YAAY,CAAC+B,OAAO,EAAE;EAC7B,IAAA;MAEA,KAAK,CAACA,OAAO,EAAE;EACjB,EAAA;;EAEA;IACAC,iBAAiBA,CAACrC,MAAM,EAAE;EACxBA,IAAAA,MAAM,CAACsC,eAAe,GAAGtC,MAAM,CAACX,QAAQ;EACxC,IAAA,OAAOW,MAAM;EACf,EAAA;EAEAU,EAAAA,kBAAkBA,GAAG;EACnB,IAAA,IAAI,IAAI,CAACC,OAAO,CAACrB,QAAQ,EAAE;EACzBmC,MAAAA,YAAY,CAACc,EAAE,CAAC,IAAI,CAAC9B,QAAQ,EAAE1C,aAAa,EAAEyE,KAAK,IAAI,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC,CAAC;EAC9E,IAAA;EAEA,IAAA,IAAI,IAAI,CAAC7B,OAAO,CAACpB,KAAK,KAAK,OAAO,EAAE;EAClCkC,MAAAA,YAAY,CAACc,EAAE,CAAC,IAAI,CAAC9B,QAAQ,EAAEzC,gBAAgB,EAAE,MAAM,IAAI,CAACuB,KAAK,EAAE,CAAC;EACpEkC,MAAAA,YAAY,CAACc,EAAE,CAAC,IAAI,CAAC9B,QAAQ,EAAExC,gBAAgB,EAAE,MAAM,IAAI,CAACuD,iBAAiB,EAAE,CAAC;EAClF,IAAA;MAEA,IAAI,IAAI,CAACb,OAAO,CAAClB,KAAK,IAAIiD,KAAK,CAACC,WAAW,EAAE,EAAE;QAC7C,IAAI,CAACC,uBAAuB,EAAE;EAChC,IAAA;EACF,EAAA;EAEAA,EAAAA,uBAAuBA,GAAG;EACxB,IAAA,KAAK,MAAMC,GAAG,IAAItC,cAAc,CAACuC,IAAI,CAAC/D,iBAAiB,EAAE,IAAI,CAAC0B,QAAQ,CAAC,EAAE;EACvEgB,MAAAA,YAAY,CAACc,EAAE,CAACM,GAAG,EAAE3E,gBAAgB,EAAEsE,KAAK,IAAIA,KAAK,CAACO,cAAc,EAAE,CAAC;EACzE,IAAA;MAEA,MAAMC,WAAW,GAAGA,MAAM;EACxB,MAAA,IAAI,IAAI,CAACrC,OAAO,CAACpB,KAAK,KAAK,OAAO,EAAE;EAClC,QAAA;EACF,MAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;QAEA,IAAI,CAACA,KAAK,EAAE;QACZ,IAAI,IAAI,CAACa,YAAY,EAAE;EACrB6C,QAAAA,YAAY,CAAC,IAAI,CAAC7C,YAAY,CAAC;EACjC,MAAA;EAEA,MAAA,IAAI,CAACA,YAAY,GAAG8C,UAAU,CAAC,MAAM,IAAI,CAAC1B,iBAAiB,EAAE,EAAEhE,sBAAsB,GAAG,IAAI,CAACmD,OAAO,CAACtB,QAAQ,CAAC;MAChH,CAAC;EAED,IAAA,MAAM8D,WAAW,GAAG;EAClBC,MAAAA,YAAY,EAAEA,MAAM,IAAI,CAACtC,MAAM,CAAC,IAAI,CAACuC,iBAAiB,CAAC1F,cAAc,CAAC,CAAC;EACvE2F,MAAAA,aAAa,EAAEA,MAAM,IAAI,CAACxC,MAAM,CAAC,IAAI,CAACuC,iBAAiB,CAACzF,eAAe,CAAC,CAAC;EACzE2F,MAAAA,WAAW,EAAEP;OACd;MAED,IAAI,CAAC3C,YAAY,GAAG,IAAIqC,KAAK,CAAC,IAAI,CAACjC,QAAQ,EAAE0C,WAAW,CAAC;EAC3D,EAAA;IAEAV,QAAQA,CAACD,KAAK,EAAE;MACd,IAAI,iBAAiB,CAACgB,IAAI,CAAChB,KAAK,CAACiB,MAAM,CAACC,OAAO,CAAC,EAAE;EAChD,MAAA;EACF,IAAA;EAEA,IAAA,MAAMC,SAAS,GAAGxE,gBAAgB,CAACqD,KAAK,CAACoB,GAAG,CAAC;EAC7C,IAAA,IAAID,SAAS,EAAE;QACbnB,KAAK,CAACO,cAAc,EAAE;QACtB,IAAI,CAACjC,MAAM,CAAC,IAAI,CAACuC,iBAAiB,CAACM,SAAS,CAAC,CAAC;EAChD,IAAA;EACF,EAAA;IAEA1B,aAAaA,CAAClC,OAAO,EAAE;MACrB,OAAO,IAAI,CAAC+B,SAAS,EAAE,CAAC+B,OAAO,CAAC9D,OAAO,CAAC;EAC1C,EAAA;IAEA+D,0BAA0BA,CAAClC,KAAK,EAAE;EAChC,IAAA,IAAI,CAAC,IAAI,CAACtB,kBAAkB,EAAE;EAC5B,MAAA;EACF,IAAA;MAEA,MAAMyD,eAAe,GAAGxD,cAAc,CAACC,OAAO,CAAC5B,eAAe,EAAE,IAAI,CAAC0B,kBAAkB,CAAC;EAExFyD,IAAAA,eAAe,CAACC,SAAS,CAACC,MAAM,CAAC3F,iBAAiB,CAAC;EACnDyF,IAAAA,eAAe,CAACG,eAAe,CAAC,cAAc,CAAC;EAE/C,IAAA,MAAMC,kBAAkB,GAAG5D,cAAc,CAACC,OAAO,CAAC,CAAA,mBAAA,EAAsBoB,KAAK,CAAA,EAAA,CAAI,EAAE,IAAI,CAACtB,kBAAkB,CAAC;EAE3G,IAAA,IAAI6D,kBAAkB,EAAE;EACtBA,MAAAA,kBAAkB,CAACH,SAAS,CAACI,GAAG,CAAC9F,iBAAiB,CAAC;EACnD6F,MAAAA,kBAAkB,CAACE,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;EACzD,IAAA;EACF,EAAA;EAEA/C,EAAAA,eAAeA,GAAG;MAChB,MAAMvB,OAAO,GAAG,IAAI,CAACG,cAAc,IAAI,IAAI,CAACgC,UAAU,EAAE;MAExD,IAAI,CAACnC,OAAO,EAAE;EACZ,MAAA;EACF,IAAA;EAEA,IAAA,MAAMuE,eAAe,GAAGC,MAAM,CAACC,QAAQ,CAACzE,OAAO,CAAC0E,YAAY,CAAC,kBAAkB,CAAC,EAAE,EAAE,CAAC;MAErF,IAAI,CAAC9D,OAAO,CAACtB,QAAQ,GAAGiF,eAAe,IAAI,IAAI,CAAC3D,OAAO,CAAC2B,eAAe;EACzE,EAAA;EAEAxB,EAAAA,MAAMA,CAACqB,KAAK,EAAEpC,OAAO,GAAG,IAAI,EAAE;MAC5B,IAAI,IAAI,CAACI,UAAU,EAAE;EACnB,MAAA;EACF,IAAA;EAEA,IAAA,MAAMuE,aAAa,GAAG,IAAI,CAACxC,UAAU,EAAE;EACvC,IAAA,MAAMyC,MAAM,GAAGxC,KAAK,KAAK1E,UAAU;MACnC,MAAMmH,WAAW,GAAG7E,OAAO,IAAI8E,6BAAoB,CAAC,IAAI,CAAC/C,SAAS,EAAE,EAAE4C,aAAa,EAAEC,MAAM,EAAE,IAAI,CAAChE,OAAO,CAACjB,IAAI,CAAC;MAE/G,IAAIkF,WAAW,KAAKF,aAAa,EAAE;EACjC,MAAA;EACF,IAAA;EAEA,IAAA,MAAMI,gBAAgB,GAAG,IAAI,CAAC7C,aAAa,CAAC2C,WAAW,CAAC;MAExD,MAAMG,YAAY,GAAGC,SAAS,IAAI;QAChC,OAAOvD,YAAY,CAACwD,OAAO,CAAC,IAAI,CAACxE,QAAQ,EAAEuE,SAAS,EAAE;EACpDE,QAAAA,aAAa,EAAEN,WAAW;EAC1BjB,QAAAA,SAAS,EAAE,IAAI,CAACwB,iBAAiB,CAAChD,KAAK,CAAC;EACxCiD,QAAAA,IAAI,EAAE,IAAI,CAACnD,aAAa,CAACyC,aAAa,CAAC;EACvC/C,QAAAA,EAAE,EAAEmD;EACN,OAAC,CAAC;MACJ,CAAC;EAED,IAAA,MAAMO,UAAU,GAAGN,YAAY,CAAClH,WAAW,CAAC;MAE5C,IAAIwH,UAAU,CAACC,gBAAgB,EAAE;EAC/B,MAAA;EACF,IAAA;EAEA,IAAA,IAAI,CAACZ,aAAa,IAAI,CAACE,WAAW,EAAE;EAClC;EACA;EACA,MAAA;EACF,IAAA;EAEA,IAAA,MAAMW,SAAS,GAAGC,OAAO,CAAC,IAAI,CAACvF,SAAS,CAAC;MACzC,IAAI,CAACV,KAAK,EAAE;MAEZ,IAAI,CAACY,UAAU,GAAG,IAAI;EAEtB,IAAA,IAAI,CAAC2D,0BAA0B,CAACgB,gBAAgB,CAAC;MACjD,IAAI,CAAC5E,cAAc,GAAG0E,WAAW;EAEjC,IAAA,MAAMa,oBAAoB,GAAGd,MAAM,GAAGlG,gBAAgB,GAAGD,cAAc;EACvE,IAAA,MAAMkH,cAAc,GAAGf,MAAM,GAAGjG,eAAe,GAAGC,eAAe;EAEjEiG,IAAAA,WAAW,CAACZ,SAAS,CAACI,GAAG,CAACsB,cAAc,CAAC;MAEzCC,eAAM,CAACf,WAAW,CAAC;EAEnBF,IAAAA,aAAa,CAACV,SAAS,CAACI,GAAG,CAACqB,oBAAoB,CAAC;EACjDb,IAAAA,WAAW,CAACZ,SAAS,CAACI,GAAG,CAACqB,oBAAoB,CAAC;MAE/C,MAAMG,gBAAgB,GAAGA,MAAM;QAC7BhB,WAAW,CAACZ,SAAS,CAACC,MAAM,CAACwB,oBAAoB,EAAEC,cAAc,CAAC;EAClEd,MAAAA,WAAW,CAACZ,SAAS,CAACI,GAAG,CAAC9F,iBAAiB,CAAC;QAE5CoG,aAAa,CAACV,SAAS,CAACC,MAAM,CAAC3F,iBAAiB,EAAEoH,cAAc,EAAED,oBAAoB,CAAC;QAEvF,IAAI,CAACtF,UAAU,GAAG,KAAK;QAEvB4E,YAAY,CAACjH,UAAU,CAAC;MAC1B,CAAC;EAED,IAAA,IAAI,CAAC+H,cAAc,CAACD,gBAAgB,EAAElB,aAAa,EAAE,IAAI,CAACoB,WAAW,EAAE,CAAC;EAExE,IAAA,IAAIP,SAAS,EAAE;QACb,IAAI,CAAC3E,KAAK,EAAE;EACd,IAAA;EACF,EAAA;EAEAkF,EAAAA,WAAWA,GAAG;MACZ,OAAO,IAAI,CAACrF,QAAQ,CAACuD,SAAS,CAAC+B,QAAQ,CAACxH,gBAAgB,CAAC;EAC3D,EAAA;EAEA2D,EAAAA,UAAUA,GAAG;MACX,OAAO3B,cAAc,CAACC,OAAO,CAAC1B,oBAAoB,EAAE,IAAI,CAAC2B,QAAQ,CAAC;EACpE,EAAA;EAEAqB,EAAAA,SAASA,GAAG;MACV,OAAOvB,cAAc,CAACuC,IAAI,CAACjE,aAAa,EAAE,IAAI,CAAC4B,QAAQ,CAAC;EAC1D,EAAA;EAEAY,EAAAA,cAAcA,GAAG;MACf,IAAI,IAAI,CAACpB,SAAS,EAAE;EAClB+F,MAAAA,aAAa,CAAC,IAAI,CAAC/F,SAAS,CAAC;QAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;EACvB,IAAA;EACF,EAAA;IAEAoD,iBAAiBA,CAACM,SAAS,EAAE;MAC3B,IAAIsC,cAAK,EAAE,EAAE;EACX,MAAA,OAAOtC,SAAS,KAAKhG,cAAc,GAAGD,UAAU,GAAGD,UAAU;EAC/D,IAAA;EAEA,IAAA,OAAOkG,SAAS,KAAKhG,cAAc,GAAGF,UAAU,GAAGC,UAAU;EAC/D,EAAA;IAEAyH,iBAAiBA,CAAChD,KAAK,EAAE;MACvB,IAAI8D,cAAK,EAAE,EAAE;EACX,MAAA,OAAO9D,KAAK,KAAKzE,UAAU,GAAGC,cAAc,GAAGC,eAAe;EAChE,IAAA;EAEA,IAAA,OAAOuE,KAAK,KAAKzE,UAAU,GAAGE,eAAe,GAAGD,cAAc;EAChE,EAAA;;EAEA;IACA,OAAOuI,eAAeA,CAAClG,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACmG,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAGxG,QAAQ,CAACyG,mBAAmB,CAAC,IAAI,EAAErG,MAAM,CAAC;EAEvD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9BoG,QAAAA,IAAI,CAACzE,EAAE,CAAC3B,MAAM,CAAC;EACf,QAAA;EACF,MAAA;EAEA,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA,IAAIoG,IAAI,CAACpG,MAAM,CAAC,KAAKsG,SAAS,IAAItG,MAAM,CAACuG,UAAU,CAAC,GAAG,CAAC,IAAIvG,MAAM,KAAK,aAAa,EAAE;EACpF,UAAA,MAAM,IAAIwG,SAAS,CAAC,CAAA,iBAAA,EAAoBxG,MAAM,GAAG,CAAC;EACpD,QAAA;EAEAoG,QAAAA,IAAI,CAACpG,MAAM,CAAC,EAAE;EAChB,MAAA;EACF,IAAA,CAAC,CAAC;EACJ,EAAA;EACF;;EAEA;EACA;EACA;;EAEAyB,YAAY,CAACc,EAAE,CAACvB,QAAQ,EAAE5C,oBAAoB,EAAEa,mBAAmB,EAAE,UAAUuD,KAAK,EAAE;EACpF,EAAA,MAAMiB,MAAM,GAAGlD,cAAc,CAACkG,sBAAsB,CAAC,IAAI,CAAC;EAE1D,EAAA,IAAI,CAAChD,MAAM,IAAI,CAACA,MAAM,CAACO,SAAS,CAAC+B,QAAQ,CAAC1H,mBAAmB,CAAC,EAAE;EAC9D,IAAA;EACF,EAAA;IAEAmE,KAAK,CAACO,cAAc,EAAE;EAEtB,EAAA,MAAM2D,QAAQ,GAAG9G,QAAQ,CAACyG,mBAAmB,CAAC5C,MAAM,CAAC;EACrD,EAAA,MAAMkD,UAAU,GAAG,IAAI,CAAClC,YAAY,CAAC,kBAAkB,CAAC;EAExD,EAAA,IAAIkC,UAAU,EAAE;EACdD,IAAAA,QAAQ,CAAC/E,EAAE,CAACgF,UAAU,CAAC;MACvBD,QAAQ,CAAClF,iBAAiB,EAAE;EAC5B,IAAA;EACF,EAAA;IAEA,IAAIoF,WAAW,CAACC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM,EAAE;MAC1DH,QAAQ,CAAC7F,IAAI,EAAE;MACf6F,QAAQ,CAAClF,iBAAiB,EAAE;EAC5B,IAAA;EACF,EAAA;IAEAkF,QAAQ,CAACvF,IAAI,EAAE;IACfuF,QAAQ,CAAClF,iBAAiB,EAAE;EAC9B,CAAC,CAAC;EAEFC,YAAY,CAACc,EAAE,CAACuE,MAAM,EAAE3I,mBAAmB,EAAE,MAAM;EACjD,EAAA,MAAM4I,SAAS,GAAGxG,cAAc,CAACuC,IAAI,CAAC5D,kBAAkB,CAAC;EAEzD,EAAA,KAAK,MAAMwH,QAAQ,IAAIK,SAAS,EAAE;EAChCnH,IAAAA,QAAQ,CAACyG,mBAAmB,CAACK,QAAQ,CAAC;EACxC,EAAA;EACF,CAAC,CAAC;;EAEF;EACA;EACA;;AAEAM,6BAAkB,CAACpH,QAAQ,CAAC;;;;;;;;"}