{"version": 3, "file": "focustrap.js", "sources": ["../../src/util/focustrap.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_KEY", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "<PERSON><PERSON><PERSON>", "autofocus", "trapElement", "DefaultType", "FocusTrap", "Config", "constructor", "config", "_config", "_getConfig", "_isActive", "_lastTabNavDirection", "activate", "focus", "EventHandler", "off", "document", "on", "event", "_handleFocusin", "_handleKeydown", "deactivate", "target", "contains", "elements", "SelectorEngine", "focusableC<PERSON><PERSON>n", "length", "key", "shift<PERSON>ey"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAW;EACxB,MAAMC,QAAQ,GAAG,cAAc;EAC/B,MAAMC,SAAS,GAAG,CAAA,CAAA,EAAID,QAAQ,CAAA,CAAE;EAChC,MAAME,aAAa,GAAG,CAAA,OAAA,EAAUD,SAAS,CAAA,CAAE;EAC3C,MAAME,iBAAiB,GAAG,CAAA,WAAA,EAAcF,SAAS,CAAA,CAAE;EAEnD,MAAMG,OAAO,GAAG,KAAK;EACrB,MAAMC,eAAe,GAAG,SAAS;EACjC,MAAMC,gBAAgB,GAAG,UAAU;EAEnC,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;EACnB,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBF,EAAAA,SAAS,EAAE,SAAS;EACpBC,EAAAA,WAAW,EAAE;EACf,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,SAAS,SAASC,MAAM,CAAC;IAC7BC,WAAWA,CAACC,MAAM,EAAE;EAClB,IAAA,KAAK,EAAE;MACP,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACF,MAAM,CAAC;MACtC,IAAI,CAACG,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC,EAAA;;EAEA;IACA,WAAWX,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWG,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWX,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;EACAoB,EAAAA,QAAQA,GAAG;MACT,IAAI,IAAI,CAACF,SAAS,EAAE;EAClB,MAAA;EACF,IAAA;EAEA,IAAA,IAAI,IAAI,CAACF,OAAO,CAACP,SAAS,EAAE;EAC1B,MAAA,IAAI,CAACO,OAAO,CAACN,WAAW,CAACW,KAAK,EAAE;EAClC,IAAA;EAEAC,IAAAA,YAAY,CAACC,GAAG,CAACC,QAAQ,EAAEtB,SAAS,CAAC,CAAA;EACrCoB,IAAAA,YAAY,CAACG,EAAE,CAACD,QAAQ,EAAErB,aAAa,EAAEuB,KAAK,IAAI,IAAI,CAACC,cAAc,CAACD,KAAK,CAAC,CAAC;EAC7EJ,IAAAA,YAAY,CAACG,EAAE,CAACD,QAAQ,EAAEpB,iBAAiB,EAAEsB,KAAK,IAAI,IAAI,CAACE,cAAc,CAACF,KAAK,CAAC,CAAC;MAEjF,IAAI,CAACR,SAAS,GAAG,IAAI;EACvB,EAAA;EAEAW,EAAAA,UAAUA,GAAG;EACX,IAAA,IAAI,CAAC,IAAI,CAACX,SAAS,EAAE;EACnB,MAAA;EACF,IAAA;MAEA,IAAI,CAACA,SAAS,GAAG,KAAK;EACtBI,IAAAA,YAAY,CAACC,GAAG,CAACC,QAAQ,EAAEtB,SAAS,CAAC;EACvC,EAAA;;EAEA;IACAyB,cAAcA,CAACD,KAAK,EAAE;MACpB,MAAM;EAAEhB,MAAAA;OAAa,GAAG,IAAI,CAACM,OAAO;MAEpC,IAAIU,KAAK,CAACI,MAAM,KAAKN,QAAQ,IAAIE,KAAK,CAACI,MAAM,KAAKpB,WAAW,IAAIA,WAAW,CAACqB,QAAQ,CAACL,KAAK,CAACI,MAAM,CAAC,EAAE;EACnG,MAAA;EACF,IAAA;EAEA,IAAA,MAAME,QAAQ,GAAGC,cAAc,CAACC,iBAAiB,CAACxB,WAAW,CAAC;EAE9D,IAAA,IAAIsB,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;QACzBzB,WAAW,CAACW,KAAK,EAAE;EACrB,IAAA,CAAC,MAAM,IAAI,IAAI,CAACF,oBAAoB,KAAKZ,gBAAgB,EAAE;QACzDyB,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CAACd,KAAK,EAAE;EACvC,IAAA,CAAC,MAAM;EACLW,MAAAA,QAAQ,CAAC,CAAC,CAAC,CAACX,KAAK,EAAE;EACrB,IAAA;EACF,EAAA;IAEAO,cAAcA,CAACF,KAAK,EAAE;EACpB,IAAA,IAAIA,KAAK,CAACU,GAAG,KAAK/B,OAAO,EAAE;EACzB,MAAA;EACF,IAAA;MAEA,IAAI,CAACc,oBAAoB,GAAGO,KAAK,CAACW,QAAQ,GAAG9B,gBAAgB,GAAGD,eAAe;EACjF,EAAA;EACF;;;;;;;;"}