{"version": 3, "file": "offcanvas.js", "sources": ["../src/offcanvas.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_LOAD_DATA_API", "ESCAPE_KEY", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "CLASS_NAME_BACKDROP", "OPEN_SELECTOR", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_RESIZE", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DISMISS", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "scroll", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "_isShown", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_addEventListeners", "toggle", "relatedTarget", "hide", "show", "showEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "_config", "ScrollBarHelper", "setAttribute", "classList", "add", "completeCallBack", "activate", "remove", "_queueCallback", "hideEvent", "deactivate", "blur", "completeCallback", "removeAttribute", "reset", "dispose", "clickCallback", "isVisible", "Boolean", "Backdrop", "className", "isAnimated", "rootElement", "parentNode", "FocusTrap", "trapElement", "on", "event", "key", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "document", "target", "SelectorEngine", "getElementFromSelector", "includes", "tagName", "preventDefault", "isDisabled", "one", "focus", "alreadyOpen", "findOne", "getInstance", "window", "selector", "find", "getComputedStyle", "position", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAeA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAW;EACxB,MAAMC,QAAQ,GAAG,cAAc;EAC/B,MAAMC,SAAS,GAAG,CAAA,CAAA,EAAID,QAAQ,CAAA,CAAE;EAChC,MAAME,YAAY,GAAG,WAAW;EAChC,MAAMC,mBAAmB,GAAG,CAAA,IAAA,EAAOF,SAAS,CAAA,EAAGC,YAAY,CAAA,CAAE;EAC7D,MAAME,UAAU,GAAG,QAAQ;EAE3B,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,kBAAkB,GAAG,SAAS;EACpC,MAAMC,iBAAiB,GAAG,QAAQ;EAClC,MAAMC,mBAAmB,GAAG,oBAAoB;EAChD,MAAMC,aAAa,GAAG,iBAAiB;EAEvC,MAAMC,UAAU,GAAG,CAAA,IAAA,EAAOT,SAAS,CAAA,CAAE;EACrC,MAAMU,WAAW,GAAG,CAAA,KAAA,EAAQV,SAAS,CAAA,CAAE;EACvC,MAAMW,UAAU,GAAG,CAAA,IAAA,EAAOX,SAAS,CAAA,CAAE;EACrC,MAAMY,oBAAoB,GAAG,CAAA,aAAA,EAAgBZ,SAAS,CAAA,CAAE;EACxD,MAAMa,YAAY,GAAG,CAAA,MAAA,EAASb,SAAS,CAAA,CAAE;EACzC,MAAMc,YAAY,GAAG,CAAA,MAAA,EAASd,SAAS,CAAA,CAAE;EACzC,MAAMe,oBAAoB,GAAG,CAAA,KAAA,EAAQf,SAAS,CAAA,EAAGC,YAAY,CAAA,CAAE;EAC/D,MAAMe,qBAAqB,GAAG,CAAA,eAAA,EAAkBhB,SAAS,CAAA,CAAE;EAE3D,MAAMiB,oBAAoB,GAAG,8BAA8B;EAE3D,MAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBH,EAAAA,QAAQ,EAAE,kBAAkB;EAC5BC,EAAAA,QAAQ,EAAE,SAAS;EACnBC,EAAAA,MAAM,EAAE;EACV,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,SAAS,SAASC,aAAa,CAAC;EACpCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC;MAEtB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACrB,IAAA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,mBAAmB,EAAE;EAC3C,IAAA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAC7C,IAAI,CAACC,kBAAkB,EAAE;EAC3B,EAAA;;EAEA;IACA,WAAWf,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWI,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWxB,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;IACAoC,MAAMA,CAACC,aAAa,EAAE;EACpB,IAAA,OAAO,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACQ,IAAI,EAAE,GAAG,IAAI,CAACC,IAAI,CAACF,aAAa,CAAC;EAC/D,EAAA;IAEAE,IAAIA,CAACF,aAAa,EAAE;MAClB,IAAI,IAAI,CAACP,QAAQ,EAAE;EACjB,MAAA;EACF,IAAA;MAEA,MAAMU,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAEhC,UAAU,EAAE;EAAE0B,MAAAA;EAAc,KAAC,CAAC;MAEpF,IAAIG,SAAS,CAACI,gBAAgB,EAAE;EAC9B,MAAA;EACF,IAAA;MAEA,IAAI,CAACd,QAAQ,GAAG,IAAI;EACpB,IAAA,IAAI,CAACC,SAAS,CAACQ,IAAI,EAAE;EAErB,IAAA,IAAI,CAAC,IAAI,CAACM,OAAO,CAACtB,MAAM,EAAE;EACxB,MAAA,IAAIuB,eAAe,EAAE,CAACR,IAAI,EAAE;EAC9B,IAAA;MAEA,IAAI,CAACK,QAAQ,CAACI,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC;MAC9C,IAAI,CAACJ,QAAQ,CAACI,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MAC5C,IAAI,CAACJ,QAAQ,CAACK,SAAS,CAACC,GAAG,CAAC1C,kBAAkB,CAAC;MAE/C,MAAM2C,gBAAgB,GAAGA,MAAM;EAC7B,MAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACtB,MAAM,IAAI,IAAI,CAACsB,OAAO,CAACxB,QAAQ,EAAE;EACjD,QAAA,IAAI,CAACY,UAAU,CAACkB,QAAQ,EAAE;EAC5B,MAAA;QAEA,IAAI,CAACR,QAAQ,CAACK,SAAS,CAACC,GAAG,CAAC3C,eAAe,CAAC;QAC5C,IAAI,CAACqC,QAAQ,CAACK,SAAS,CAACI,MAAM,CAAC7C,kBAAkB,CAAC;QAClDkC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE/B,WAAW,EAAE;EAAEyB,QAAAA;EAAc,OAAC,CAAC;MACrE,CAAC;MAED,IAAI,CAACgB,cAAc,CAACH,gBAAgB,EAAE,IAAI,CAACP,QAAQ,EAAE,IAAI,CAAC;EAC5D,EAAA;EAEAL,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;EAClB,MAAA;EACF,IAAA;MAEA,MAAMwB,SAAS,GAAGb,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE9B,UAAU,CAAC;MAEjE,IAAIyC,SAAS,CAACV,gBAAgB,EAAE;EAC9B,MAAA;EACF,IAAA;EAEA,IAAA,IAAI,CAACX,UAAU,CAACsB,UAAU,EAAE;EAC5B,IAAA,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE;MACpB,IAAI,CAAC1B,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACa,QAAQ,CAACK,SAAS,CAACC,GAAG,CAACzC,iBAAiB,CAAC;EAC9C,IAAA,IAAI,CAACuB,SAAS,CAACO,IAAI,EAAE;MAErB,MAAMmB,gBAAgB,GAAGA,MAAM;QAC7B,IAAI,CAACd,QAAQ,CAACK,SAAS,CAACI,MAAM,CAAC9C,eAAe,EAAEE,iBAAiB,CAAC;EAClE,MAAA,IAAI,CAACmC,QAAQ,CAACe,eAAe,CAAC,YAAY,CAAC;EAC3C,MAAA,IAAI,CAACf,QAAQ,CAACe,eAAe,CAAC,MAAM,CAAC;EAErC,MAAA,IAAI,CAAC,IAAI,CAACb,OAAO,CAACtB,MAAM,EAAE;EACxB,QAAA,IAAIuB,eAAe,EAAE,CAACa,KAAK,EAAE;EAC/B,MAAA;QAEAlB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE5B,YAAY,CAAC;MACnD,CAAC;MAED,IAAI,CAACsC,cAAc,CAACI,gBAAgB,EAAE,IAAI,CAACd,QAAQ,EAAE,IAAI,CAAC;EAC5D,EAAA;EAEAiB,EAAAA,OAAOA,GAAG;EACR,IAAA,IAAI,CAAC7B,SAAS,CAAC6B,OAAO,EAAE;EACxB,IAAA,IAAI,CAAC3B,UAAU,CAACsB,UAAU,EAAE;MAC5B,KAAK,CAACK,OAAO,EAAE;EACjB,EAAA;;EAEA;EACA5B,EAAAA,mBAAmBA,GAAG;MACpB,MAAM6B,aAAa,GAAGA,MAAM;EAC1B,MAAA,IAAI,IAAI,CAAChB,OAAO,CAACxB,QAAQ,KAAK,QAAQ,EAAE;UACtCoB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE7B,oBAAoB,CAAC;EACzD,QAAA;EACF,MAAA;QAEA,IAAI,CAACwB,IAAI,EAAE;MACb,CAAC;;EAED;MACA,MAAMwB,SAAS,GAAGC,OAAO,CAAC,IAAI,CAAClB,OAAO,CAACxB,QAAQ,CAAC;MAEhD,OAAO,IAAI2C,QAAQ,CAAC;EAClBC,MAAAA,SAAS,EAAExD,mBAAmB;QAC9BqD,SAAS;EACTI,MAAAA,UAAU,EAAE,IAAI;EAChBC,MAAAA,WAAW,EAAE,IAAI,CAACxB,QAAQ,CAACyB,UAAU;EACrCP,MAAAA,aAAa,EAAEC,SAAS,GAAGD,aAAa,GAAG;EAC7C,KAAC,CAAC;EACJ,EAAA;EAEA3B,EAAAA,oBAAoBA,GAAG;MACrB,OAAO,IAAImC,SAAS,CAAC;QACnBC,WAAW,EAAE,IAAI,CAAC3B;EACpB,KAAC,CAAC;EACJ,EAAA;EAEAR,EAAAA,kBAAkBA,GAAG;MACnBM,YAAY,CAAC8B,EAAE,CAAC,IAAI,CAAC5B,QAAQ,EAAEzB,qBAAqB,EAAEsD,KAAK,IAAI;EAC7D,MAAA,IAAIA,KAAK,CAACC,GAAG,KAAKpE,UAAU,EAAE;EAC5B,QAAA;EACF,MAAA;EAEA,MAAA,IAAI,IAAI,CAACwC,OAAO,CAACvB,QAAQ,EAAE;UACzB,IAAI,CAACgB,IAAI,EAAE;EACX,QAAA;EACF,MAAA;QAEAG,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE7B,oBAAoB,CAAC;EAC3D,IAAA,CAAC,CAAC;EACJ,EAAA;;EAEA;IACA,OAAO4D,eAAeA,CAAC7C,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAAC8C,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAGnD,SAAS,CAACoD,mBAAmB,CAAC,IAAI,EAAEhD,MAAM,CAAC;EAExD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA;EACF,MAAA;EAEA,MAAA,IAAI+C,IAAI,CAAC/C,MAAM,CAAC,KAAKiD,SAAS,IAAIjD,MAAM,CAACkD,UAAU,CAAC,GAAG,CAAC,IAAIlD,MAAM,KAAK,aAAa,EAAE;EACpF,QAAA,MAAM,IAAImD,SAAS,CAAC,CAAA,iBAAA,EAAoBnD,MAAM,GAAG,CAAC;EACpD,MAAA;EAEA+C,MAAAA,IAAI,CAAC/C,MAAM,CAAC,CAAC,IAAI,CAAC;EACpB,IAAA,CAAC,CAAC;EACJ,EAAA;EACF;;EAEA;EACA;EACA;;EAEAY,YAAY,CAAC8B,EAAE,CAACU,QAAQ,EAAEhE,oBAAoB,EAAEE,oBAAoB,EAAE,UAAUqD,KAAK,EAAE;EACrF,EAAA,MAAMU,MAAM,GAAGC,cAAc,CAACC,sBAAsB,CAAC,IAAI,CAAC;EAE1D,EAAA,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACC,OAAO,CAAC,EAAE;MACxCd,KAAK,CAACe,cAAc,EAAE;EACxB,EAAA;EAEA,EAAA,IAAIC,mBAAU,CAAC,IAAI,CAAC,EAAE;EACpB,IAAA;EACF,EAAA;EAEA/C,EAAAA,YAAY,CAACgD,GAAG,CAACP,MAAM,EAAEnE,YAAY,EAAE,MAAM;EAC3C;EACA,IAAA,IAAI+C,kBAAS,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC4B,KAAK,EAAE;EACd,IAAA;EACF,EAAA,CAAC,CAAC;;EAEF;EACA,EAAA,MAAMC,WAAW,GAAGR,cAAc,CAACS,OAAO,CAAClF,aAAa,CAAC;EACzD,EAAA,IAAIiF,WAAW,IAAIA,WAAW,KAAKT,MAAM,EAAE;MACzCzD,SAAS,CAACoE,WAAW,CAACF,WAAW,CAAC,CAACrD,IAAI,EAAE;EAC3C,EAAA;EAEA,EAAA,MAAMsC,IAAI,GAAGnD,SAAS,CAACoD,mBAAmB,CAACK,MAAM,CAAC;EAClDN,EAAAA,IAAI,CAACxC,MAAM,CAAC,IAAI,CAAC;EACnB,CAAC,CAAC;EAEFK,YAAY,CAAC8B,EAAE,CAACuB,MAAM,EAAE1F,mBAAmB,EAAE,MAAM;IACjD,KAAK,MAAM2F,QAAQ,IAAIZ,cAAc,CAACa,IAAI,CAACtF,aAAa,CAAC,EAAE;MACzDe,SAAS,CAACoD,mBAAmB,CAACkB,QAAQ,CAAC,CAACxD,IAAI,EAAE;EAChD,EAAA;EACF,CAAC,CAAC;EAEFE,YAAY,CAAC8B,EAAE,CAACuB,MAAM,EAAE9E,YAAY,EAAE,MAAM;IAC1C,KAAK,MAAMY,OAAO,IAAIuD,cAAc,CAACa,IAAI,CAAC,8CAA8C,CAAC,EAAE;MACzF,IAAIC,gBAAgB,CAACrE,OAAO,CAAC,CAACsE,QAAQ,KAAK,OAAO,EAAE;QAClDzE,SAAS,CAACoD,mBAAmB,CAACjD,OAAO,CAAC,CAACU,IAAI,EAAE;EAC/C,IAAA;EACF,EAAA;EACF,CAAC,CAAC;AAEF6D,4CAAoB,CAAC1E,SAAS,CAAC;;EAE/B;EACA;EACA;;AAEA2E,6BAAkB,CAAC3E,SAAS,CAAC;;;;;;;;"}