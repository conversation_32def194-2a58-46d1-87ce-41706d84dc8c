<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm" crossorigin="anonymous"></script>
    <script src="./node_modules/axios/dist/axios.min.js"></script>
    <title>Create Post</title>
</head>
<body class="createBody">
    <a href="index.html" class="btn btn-primary position-absolute top-0 start-0 m-3">back</a>
    <!-- create post form -->
    <div class="createContainer">
        <div class="col-12 col-lg-8 mx-auto createPostSection">
            <div class="create-post-container">
                <div class="create-post-header">
                    <h2>Create New Post</h2>
                    <p>Share your thoughts, ideas, and experiences with the community</p>
                </div>
                
                <form id="createPostForm" onsubmit="event.preventDefault();">
                    <div class="form-group">
                        <label for="postTitle" class="form-label">
                            Post Title
                        </label>
                        <input type="text" class="form-control" id="postTitle" placeholder="Enter a catchy title for your post" required maxlength="100">
                        <div class="character-count" id="titleCount">0/100</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="postBody" class="form-label">
                            Post Content
                        </label>
                        <textarea class="form-control" id="postBody" placeholder="What's on your mind? Share your thoughts here..." required maxlength="1000"></textarea>
                        <div class="character-count" id="bodyCount">0/1000</div>
                    </div>

                    <div class="form-group">
                        <label for="postImage" class="form-label">
                            Post Image (Optional)
                        </label>
                        <input class="form-control" id="postImage" type="file"/>
                    </div>

                    <button type="submit" class="btn btn-create-post" id="createPostBtn" 
                    onclick="
                        if(postTitle.value.trim() === '' || postBody.value.trim() === '') {
                            alert('Please fill in all required fields.');
                            return;
                        }else {
                            createPost(); 
                            window.location.href='index.html';
                            alert('Post created successfully!');
                            getPosts();
                        }
                        ">
                        Publish Post
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>
<script src="app.js"></script>
</html>