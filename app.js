let backendApi = "https://tarmeezacademy.com/api/v1";
let navBar = document.getElementById("navBar");
//register:----------------
let registerBtn = document.getElementById("registerButton");
let usernameReg = document.getElementById("registerUsername");
let passwordReg = document.getElementById("registerPassword");
let nameReg = document.getElementById("registerName");
let emailReg = document.getElementById("registerEmail");
let imageReg = document.getElementById("profileImage");
let imgUpload = document.getElementById("imageUpload");
//register:----------------

//login:----------------
let loginBtn = document.getElementById("loginButton");
let usernameLogin = document.getElementById("loginUsername");
let passwordLogin = document.getElementById("loginPassword");
//login:----------------

//posts:----------------
let postsContainer = document.getElementById("postsContainer");
//posts:----------------

//create posts:----------------
let createPostSection = document.getElementById("createPostSection");
let postTitle = document.getElementById("postTitle");
let postBody = document.getElementById("postBody");
let postImage = document.getElementById("postImage");
let createPostBtn = document.getElementById("createPostBtn");
//create posts:----------------

//tags:----------------
let tagsContainer = document.getElementById("tagsContainer");
//tags:----------------

//register
function register() {
    imgUpload.onchange = (event) =>{
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
                imageReg.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    };

    let formData = new FormData();
    formData.append("username", usernameReg.value);
    formData.append("name", nameReg.value);
    formData.append("password", passwordReg.value);
    formData.append("image", imgUpload.files[0]);
    formData.append("email", emailReg.value);
    registerBtn.onclick = () =>{
        axios.post(backendApi + "/register", formData, {
            headers: {
                "Content-Type": "multipart/form-data",
                Authorization: "Bearer " + localStorage.getItem("token"),
                Accept: "application/json",
            }
        })
        .then((response) => {
            console.log("Api register",response.data);  
            localStorage.setItem("token", response.data.token);
            window.location.href = "index.html";
        })
        .catch((error) => {
            console.log(error);
            alert(error.response.data.message);
        });
    }
}

//login
function login() {
    loginBtn.onclick = () =>{
        axios.post(backendApi + "/login",{
            username:usernameLogin.value,
            password:passwordLogin.value
        },{
            headers: {
                Authorization: "Bearer " + localStorage.getItem("token"),
                Accept: "application/json",
            }
        })
        .then((response) => {
            console.log("Api login",response.data);  
            localStorage.setItem("token", response.data.token);
            window.location.href = "index.html";
        })
        .catch((error) => {
            console.log(error);
            alert(error.response.data.message);
        });
    }
}

if(registerBtn){
    register();
}
if(loginBtn){
    login();
}

function updateNavBar() {
    if (localStorage.getItem("token")) {
        navBar.innerHTML = `
            <div class="container-fluid">
                <a class="navbar-brand" href="#">Tarmeez</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="#">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#profile">Profile</a>
                        </li>
                        <li class="nav-item d-flex align-items-center">
                            <label title="Toggle dark mode ">
                                <input class="toggle-checkbox" type="checkbox" title="Toggle dark mode">
                                <div class="toggle-slot">
                                    <div class="sun-icon-wrapper">
                                        <div class="iconify sun-icon" data-icon="feather-sun" data-inline="false"></div>
                                    </div>
                                    <div class="toggle-button"></div>
                                    <div class="moon-icon-wrapper">
                                        <div class="iconify moon-icon" data-icon="feather-moon" data-inline="false"></div>
                                    </div>
                                </div>
                            </label>
                        </li>
                    </ul>
                    <form class="d-flex gap-2" id="navForm">
                        <button 
                            class="btn btn-outline-primary" 
                            type="button" 
                            id="navCreatePostBtn"
                            onclick="window.location.href='create_post.html';"
                        >Create Post</button>

                        <button 
                            class="btn btn-outline-danger" 
                            type="button" 
                            id="navLogoutBtn"
                            onclick="
                                window.location.href='login.html'; 
                                localStorage.removeItem('token'); 
                                updateNavBar();
                            "
                        >Logout</button>
                    </form>
                </div>
            </div>
        `;
    } else {
        navBar.innerHTML = `
            <div class="container-fluid">
                <a class="navbar-brand" href="#">Tarmeez</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="#">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#profile">Profile</a>
                        </li>
                        <li class="nav-item d-flex align-items-center">
                            <label title="Toggle dark mode ">
                                <input class="toggle-checkbox" type="checkbox" title="Toggle dark mode">
                                <div class="toggle-slot">
                                    <div class="sun-icon-wrapper">
                                        <div class="iconify sun-icon" data-icon="feather-sun" data-inline="false"></div>
                                    </div>
                                    <div class="toggle-button"></div>
                                    <div class="moon-icon-wrapper">
                                        <div class="iconify moon-icon" data-icon="feather-moon" data-inline="false"></div>
                                    </div>
                                </div>
                            </label>
                        </li>
                    </ul>
                    <form class="d-flex gap-2" id="navForm">
                        <button 
                            class="btn btn-outline-primary" 
                            type="button" 
                            id="navLoginBtn"
                            onclick="window.location.href='login.html';"
                        >Login</button>

                        <button 
                            class="btn btn-outline-success" 
                            type="button" 
                            id="navRegisterBtn"
                            onclick="window.location.href='register.html';"
                        >Register</button>
                    </form>
                </div>
            </div>
        `;
    }
}

updateNavBar();

//users
axios.get(backendApi + "/users")
    .then((response) => {
        console.log("Api Users",response.data.data);  
    })
    .catch((error) => {
        console.log(error);
    });

//show users
axios.get(backendApi + "/users/1")
    .then((response) => {
        console.log("Api show user",response.data.data);  
    })
    .catch((error) => {
        console.log(error);
    });
    
//user posts
axios.get(backendApi + "/users/1/posts")
    .then((response) => {
        console.log("Api user posts",response.data.data);  
    })
    .catch((error) => {
        console.log(error);
    });
    
//posts
function getPosts() {
    postsContainer.innerHTML = `
        <div class="loading" id="loadingIndicator">
            <p>Loading posts...</p>
        </div>`;
    axios.get(backendApi + "/posts?limit=10",{
        headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
            Accept: "application/json",
        }
    })
    .then((response) => {
        console.log("Api posts",response.data.data);  
        console.log("Api posts22",response.data.data[0].author); 

        if(response.data.data.length === 0 || postsContainer.innerHTML === ""){
            postsContainer.innerHTML = `
                <div>
                    <h1>Loading Posts...</h1>
                </div>
            `;
        }
        postsContainer.innerHTML = "";
        response.data.data.forEach((post) => {
            postsContainer.innerHTML += `
                <div class="card mb-3 shadow" id="postCard">
                    <div class="card-header">
                        <div>
                            <img id="postProfileImage" src="${post.author.profile_image}" alt="no image" width="30" height="30" class="border border-2 rounded-circle">
                            <span id="postUsername">${post.author.name}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <img id="postImage" src="${post.image}" alt="" class="rounded mb-1" width="100%" height="400px">
                        <h6 id="postTime" class="card-title time">${post.created_at}</h6>
                        <h5 id="postTitle" class="card-title">${post.title}</h5>
                        <p id="postText" class="card-text">${post.body}</p>
                        <div>
                            <hr>
                            <div class="d-flex align-items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pen" viewBox="0 0 16 16">
                                    <path d="m13.498.795.149-.149a1.207 1.207 0 1 1 1.707 1.708l-.149.148a1.5 1.5 0 0 1-.059 2.059L4.854 14.854a.5.5 0 0 1-.233.131l-4 1a.5.5 0 0 1-.606-.606l1-4a.5.5 0 0 1 .131-.232l9.642-9.642a.5.5 0 0 0-.642.056L6.854 4.854a.5.5 0 1 1-.708-.708L9.44.854A1.5 1.5 0 0 1 11.5.796a1.5 1.5 0 0 1 1.998-.001m-.644.766a.5.5 0 0 0-.707 0L1.95 11.756l-.764 3.057 3.057-.764L14.44 3.854a.5.5 0 0 0 0-.708z"/>
                                </svg>
                                <span style="margin-right: 10px;">(${post.comments_count}) Comments</span>
                                <span id="tagsContainer-${post.id}">
                                    <button class="btn btn-sm rounded-5" style="background-color: gray; color: white;">Tags</button>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById(`tagsContainer-${post.id}`).innerHTML = "";
            console.log("Api tagssssssss",post.id , post.tags);
            post.tags.forEach((tag) => {
                console.log("Api tags name",tag.name);
                document.getElementById(`tagsContainer-${post.id}`).innerHTML += `
                    <button class="btn btn-sm rounded-5" style="background-color: gray; color: white;">${tag.name || "No tags"}</button>    
                `;
            })
        })
    })
    .catch((error) => {
        console.log(error);
    });
}

getPosts();

//show posts
axios.get(backendApi + "/posts/1")
    .then((response) => {
        console.log("Api show post",response.data.data);  
    })
    .catch((error) => {
        console.log(error);
    });
    
//create posts
function createPost() {
    let formData = new FormData();
    formData.append("title", postTitle.value);
    formData.append("body", postBody.value);
    formData.append("image", postImage.files[0]);
    axios.post(backendApi + "/posts", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
            Authorization: "Bearer " + localStorage.getItem("token"),
            Accept: "application/json",
        }
    })
    .then((response) => {
        console.log("Api create post",response.data.data);        
        createPostSection.innerHTML = "";
        createPostSection.innerHTML = `
        <div class="col-12 col-lg-8 mx-auto" id="createPostSection">
            <div class="create-post-container">
                <div class="create-post-header">
                    <h2>Create New Post</h2>
                    <p>Share your thoughts, ideas, and experiences with the community</p>
                </div>
                
                <form id="createPostForm" onsubmit="event.preventDefault();">
                    <div class="form-group">
                        <label for="postTitle" class="form-label">
                            Post Title
                        </label>
                        <input type="text" class="form-control" id="postTitle" placeholder="Enter a catchy title for your post" required maxlength="100">
                        <div class="character-count" id="titleCount">0/100</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="postBody" class="form-label">
                            Post Content
                        </label>
                        <textarea class="form-control" id="postBody" placeholder="What's on your mind? Share your thoughts here..." required maxlength="1000"></textarea>
                        <div class="character-count" id="bodyCount">0/1000</div>
                    </div>

                    <div class="form-group">
                        <label for="postImage" class="form-label">
                            Post Image (Optional)
                        </label>
                        <input class="form-control" id="postImage" type="file"/>
                    </div>

                    <button type="submit" class="btn btn-create-post" id="createPostBtn">
                        Publish Post
                    </button>
                </form>
            </div>
        </div>`;
    })
    .catch((error) => {
        console.log(error);
    });
}