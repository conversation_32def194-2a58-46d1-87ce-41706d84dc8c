<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm" crossorigin="anonymous"></script>
    <script src="./node_modules/axios/dist/axios.min.js"></script>
    <title>register</title>
</head>
<body class="regBody">
    <!-- <form id="registerForm" onsubmit="event.preventDefault();">
        <input type="text" id="username" placeholder="Username" required>
        <input type="password" id="password" placeholder="Password" required>
        <img src="" alt="no image" id="profileImage" width="30" height="30" class="border border-2 rounded-circle">
        <input type="text" id="name" placeholder="Name" required>
        <input type="email" id="email" placeholder="Email" required>
        <button type="submit" id="registerButton">Register</button>
    </form> -->

    <a href="index.html" class="btn btn-primary position-absolute top-0 start-0 m-3">back</a>

    <div class="register-container">
        <div class="register-header">
            <h2>Create Account</h2>
        </div>
        
        <div class="register-body">
            <form id="registerForm" onsubmit="event.preventDefault();">
                <div class="profile-image-container">
                    <img src="" alt="profile image" class="profile-image" id="profileImage">
                    <label for="imageUpload" class="upload-btn">Upload Profile Image</label>
                    <input type="file" id="imageUpload">
                </div>
                
                <div class="mb-3">
                    <!-- <input type="text" class="form-control" id="registerUsername" placeholder="Username" required> -->
                    <input type="text" class="form-control" id="registerUsername" placeholder="Username" required>
                </div>
                
                <div class="mb-3">
                    <input type="text" class="form-control" id="registerName" placeholder="Name" required>
                </div>
                
                <div class="mb-3">
                    <input type="email" class="form-control" id="registerEmail" placeholder="Email" required>
                </div>
                
                <div class="mb-3 position-relative">
                    <input type="password" class="form-control" id="registerPassword" placeholder="Password" required>
                </div>
                
                <button type="submit" class="btn btn-register" id="registerButton">
                    Create Account
                </button>
            </form>
            
            <div class="login-link">
                Have an account? <a href="login.html">Login</a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <i class="fas fa-shield-alt"></i>
                    <p>Secure</p>
                </div>
                <div class="feature">
                    <i class="fas fa-bolt"></i>
                    <p>Fast</p>
                </div>
                <div class="feature">
                    <i class="fas fa-users"></i>
                    <p>Social</p>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="app.js"></script>
</html>